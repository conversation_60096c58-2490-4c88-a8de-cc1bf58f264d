package com.ybmmarket20.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.CommodityRecyclerAdapter;
import com.ybmmarket20.bean.ProductDetailKeyAttr;
import com.ybmmarket20.bean.ProductInstructionBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date    2025.3.28
 * @desc    商品详情->商品信息添加中药扩展属性描述； 原UI不是list，但扩展是列表，加载自定义item
 */
public class ProductDetailCnmExtLayout extends RelativeLayout {

    public ProductDetailCnmExtLayout(Context context) {
        this(context, null);
    }

    public ProductDetailCnmExtLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ProductDetailCnmExtLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initData();
    }
    private TextView tvTitle;
    private TextView tvValue;
    private ProductDetailKeyAttr data = null;

    private void initData() {
        View.inflate(getContext(), R.layout.product_detail_cnmext, this);

        tvTitle = findViewById(R.id.tvTitle);
        tvValue = findViewById(R.id.tvValue);

    }

    /**
     * 设置轮播图数据
     *
     * @param
     */
    public void setItemData(ProductDetailKeyAttr data) {
        if (data == null) {
            return;
        }
        this.data = data;
        tvTitle.setText(data.attrTitle+":");
        tvValue.setText(data.attrValue);
    }
}
