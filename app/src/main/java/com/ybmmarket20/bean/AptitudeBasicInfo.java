package com.ybmmarket20.bean;

public class AptitudeBasicInfo {

    /**
     * address : 注册地址001
     * area : 湖北省武汉市汉阳区
     * cityId : 420100
     * customerType : 1
     * customerTypeName : 单体药店
     * districtId : 汉阳区
     * id : 1500123409
     * mobile : 18701380001
     * nickname : 徐犊犊
     * provinceId : 420000
     * realName : 测试武汉智慧脸药店
     * registerAddress : 湖北省武汉市汉阳区注册地址001
     * registerCode : XS420000
     * scopeOfExperience : 中成药,化学药制剂,抗生素制剂,生化药品,保健食品,预包装食品,处方药（禁止类除外）,非处方药（甲类、乙类）,消毒用品,化妆品,日用百货,乳制品,I类.6821,I类.6824,I类.6826,I类.6801,I类.6802,I类.6803,I类.6804,I类.6805,I类.6806,I类.6807,I类.6808,I类.6809,I类.6810,I类.6812,I类.6813,I类.6815,I类.6816,I类.6820,I类.6822,I类.6823,I类.6827,I类.6831,I类.6834,I类.6840,I类.6841,I类.6854,I类.6855,I类.6856,I类.6857,I类.6858,I类.6863,I类.6864,I类.6866,II类.6801,II类.6802,II类.6803,II类.6804,II类.6805,II类.6806,II类.6807,II类.6808,II类.6809,II类.6810,II类.6812,II类.6813,II类.6815,II类.6816,II类.6820,II类.6821,II类.6822,II类.6823,II类.6824,II类.6825,II类.6826,II类.6827,II类.6828,II类.6830,II类.6831,II类.6832,II类.6833,II类.6834,II类.6840,II类.6841,II类.6845,II类.6846,II类.6854,II类.6855,II类.6856,II类.6857,II类.6858,II类.6863,II类.6864,II类.6865,II类.6866,II类.6870,II类.6877,日化产品,I类,II类
     * streetId : 420105013
     * syncNo : SYNC1500123409
     * type : 1
     */

    private String address;
    private String area;
    private int cityId;
    private int customerType;
    private String customerTypeName;
    private String districtId;
    private int id;
    private String mobile;
    private String nickname;
    private int provinceId;
    private String realName;
    private String registerAddress;
    private String registerCode;
    private String scopeOfExperience;
    private int streetId;
    private String syncNo;
    private int type;
    /** 发票类型 */
    private String invoiceType;

    /** 发票名称 */
    private String invoiceName;
    public boolean remark;
    public String code;

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceName() {
        return invoiceName;
    }

    public void setInvoiceName(String invoiceName) {
        this.invoiceName = invoiceName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public int getCustomerType() {
        return customerType;
    }

    public void setCustomerType(int customerType) {
        this.customerType = customerType;
    }

    public String getCustomerTypeName() {
        return customerTypeName;
    }

    public void setCustomerTypeName(String customerTypeName) {
        this.customerTypeName = customerTypeName;
    }

    public String getDistrictId() {
        return districtId;
    }

    public void setDistrictId(String districtId) {
        this.districtId = districtId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public int getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(int provinceId) {
        this.provinceId = provinceId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getRegisterCode() {
        return registerCode;
    }

    public void setRegisterCode(String registerCode) {
        this.registerCode = registerCode;
    }

    public String getScopeOfExperience() {
        return scopeOfExperience;
    }

    public void setScopeOfExperience(String scopeOfExperience) {
        this.scopeOfExperience = scopeOfExperience;
    }

    public int getStreetId() {
        return streetId;
    }

    public void setStreetId(int streetId) {
        this.streetId = streetId;
    }

    public String getSyncNo() {
        return syncNo;
    }

    public void setSyncNo(String syncNo) {
        this.syncNo = syncNo;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
