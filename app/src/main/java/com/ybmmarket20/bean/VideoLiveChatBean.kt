package com.ybmmarket20.bean


/**
 * <AUTHOR>
 * 群组发送文本消息数据
 */

//直播端右下角listview显示type
const val TEXT_TYPE: Int = 0
const val MEMBER_ENTER = 1
const val MEMBER_EXIT = 2
const val PRAISE = 3

class VideoLiveChatBean(
        var senderName: String?,    // 发送者的名字
        var content: String?,       // 消息内容
        var type: Int,               // 消息类型
        var isSelf: Boolean = false
) {

    fun getTransformSenderName(): String? {
        if (isSelf) {
            senderName = "我"
        }
        return senderName
    }

    override fun equals(other: Any?): Bo<PERSON>an {
        if (this === other) return true
        if (other !is VideoLiveChatBean) return false
        val that = other as VideoLiveChatBean
        if (type != that.type) return false
        if (if (senderName != null) senderName != that.senderName else that.senderName != null) return false
        return if (content != null) content == that.content else that.content == null
    }

    override fun hashCode(): Int {
        var result = if (senderName != null) senderName.hashCode() else 0
        result = 31 * result + if (content != null) content.hashCode() else 0
        result = 31 * result + type
        return result
    }
}