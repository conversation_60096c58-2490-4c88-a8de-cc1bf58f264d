<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item_collect"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="horizontal">

    <CheckBox
        android:id="@+id/checkbox"
        android:layout_width="@dimen/dimen_dp_17"
        android:layout_height="@dimen/dimen_dp_17"
        android:layout_marginLeft="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_49"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:background="@drawable/selector_cart_bottom_coupon_check"
        android:button="@null"
        android:visibility="gone"
        tools:visibility="gone" />

    <include layout="@layout/item_goods_new" />

</LinearLayout>