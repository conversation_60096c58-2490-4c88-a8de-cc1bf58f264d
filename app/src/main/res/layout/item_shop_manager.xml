<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvShopName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_15"
        android:textColor="@color/color_292933"
        android:text="监利县四季康药店天府路店"
        android:layout_marginStart="@dimen/dimen_dp_17"
        android:layout_marginEnd="@dimen/dimen_dp_60"
        android:layout_marginTop="@dimen/dimen_dp_12"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvShopAddress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_676773"
        android:text="监利县四季康药店天府路店"
        android:layout_marginStart="@dimen/dimen_dp_17"
        android:layout_marginEnd="@dimen/dimen_dp_60"
        android:layout_marginTop="@dimen/dimen_dp_4"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvShopName" />

    <ImageView
        android:id="@+id/ivEditStatus"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_18"
        android:src="@drawable/icon_shop_info_delete"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        app:layout_constraintBottom_toBottomOf="@+id/divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvCheckClerk"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="查看店员"
        app:layout_constraintStart_toStartOf="@+id/tvShopAddress"
        app:layout_constraintTop_toBottomOf="@+id/tvShopAddress"
        android:textSize="@dimen/dimen_dp_11"
        android:background="@drawable/shape_button_quite"
        android:textColor="#1ABB85"
        android:layout_marginTop="@dimen/dimen_dp_7"
        android:paddingStart="@dimen/dimen_dp_5"
        android:paddingEnd="@dimen/dimen_dp_5"
        android:paddingBottom="@dimen/dimen_dp_2"
        android:paddingTop="@dimen/dimen_dp_2"/>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="#F5F5F5"
        android:layout_marginTop="@dimen/dimen_dp_15"
        app:layout_constraintTop_toBottomOf="@+id/tvCheckClerk" />

</androidx.constraintlayout.widget.ConstraintLayout>