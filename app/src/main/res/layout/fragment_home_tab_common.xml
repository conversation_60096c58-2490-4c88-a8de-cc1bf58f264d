<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:background="@color/colors_eeeeee"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smart_refresh_layout"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_height="0dp">

            <com.ybmmarket20.view.HomeSteadyHeader
                android:layout_width="match_parent"
                android:background="@color/transparent"
                android:layout_height="@dimen/dimen_dp_70" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_content"
                android:layout_width="match_parent"
                android:overScrollMode="never"
                android:layout_height="match_parent"/>

            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_ad_bottom"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="11dp"
            android:visibility="gone"
            android:layout_marginHorizontal="8dp"
            android:background="@android:color/transparent"
            android:layout_height="wrap_content">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_ad"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="9"
                android:layout_width="0dp"
                app:shapeAppearance="@style/ShapeableImageView_13dp_rounded_corner"
                android:scaleType="centerCrop"
                android:adjustViewBounds="true"
                android:layout_height="0dp"/>

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="18dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="10dp"
                android:src="@drawable/icon_left_close"
                android:layout_height="18dp"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
