<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@+id/ll_root"
                xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                android:gravity="center">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:id="@+id/ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="50dp"
        android:orientation="vertical"
        app:rv_backgroundColor="#FF4146"
        app:rv_cornerRadius="13dp">

        <TextView
            android:id="@+id/tv_01"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="75dp"
            android:text="天降80元大礼包"
            android:textColor="@color/white"
            android:textSize="17sp"
            android:textStyle="bold"/>

        <TextView
            android:id="@+id/tv_02"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="5dp"
            android:text="恭喜你5张劵收入囊中"
            android:textColor="@color/white"
            android:textSize="13sp"/>

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/list"
            android:layout_width="match_parent"
            android:layout_height="200dp"/>

    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <RelativeLayout
        android:id="@+id/rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:src="@drawable/icon_ad_discount_coupon_01"/>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="15dp"
            android:src="@drawable/icon_ad_discount_coupon_02"/>

    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ll"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:src="@drawable/icon_ad_discount_coupon_03"/>

</RelativeLayout>