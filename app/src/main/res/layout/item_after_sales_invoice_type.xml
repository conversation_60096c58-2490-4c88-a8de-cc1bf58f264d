<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_44"
    android:layout_marginTop="@dimen/dimen_dp_10"
    android:background="@color/white">

    <com.ybmmarket20.view.textview.RequiredTitleTextView
        android:id="@+id/tvAfterSalesType"
        style="@style/afterSalesItemTitle"
        android:text="发票售后类型"
        android:layout_marginStart="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvAfterSalesType">

        <TextView
            android:id="@+id/tvNoInvoice"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_30"
            android:textSize="@dimen/dimen_dp_12"
            android:text="无票"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:background="@drawable/selector_company_license"
            android:gravity="center"
            android:textColor="@color/selector_after_sales_invoice_type"
            android:layout_gravity="center_vertical"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvErrorInvoice"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_30"
            android:textSize="@dimen/dimen_dp_12"
            android:text="错票"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:background="@drawable/selector_company_license"
            android:gravity="center"
            android:textColor="@color/selector_after_sales_invoice_type"
            android:layout_gravity="center_vertical"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvSpecialInvoice"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_30"
            android:textSize="@dimen/dimen_dp_12"
            android:text="申请专票"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:background="@drawable/selector_company_license"
            android:gravity="center"
            android:textColor="@color/selector_after_sales_invoice_type"
            android:layout_gravity="center_vertical"
            android:layout_weight="1" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>