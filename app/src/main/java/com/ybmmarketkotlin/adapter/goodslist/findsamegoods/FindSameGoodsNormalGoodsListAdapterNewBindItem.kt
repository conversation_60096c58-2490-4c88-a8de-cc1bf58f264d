package com.ybmmarketkotlin.adapter.goodslist.findsamegoods

import android.content.Context
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarketkotlin.adapter.goodslist.NormalGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.bigpic.NormalGoodsListAdapterNewBindItemBigPic

/**
 * 找相似 普通商品
 */
class FindSameGoodsNormalGoodsListAdapterNewBindItem(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: Rows<PERSON>ean,
    countDownTimerMap: SparseArray<CountDownTimer>
): NormalGoodsListAdapterNewBindItemBigPic(mContext, baseViewHolder, rowsBean, countDownTimerMap, false) {

    override fun onShop(isShowShopInfo: Boolean) {
        super.onShop(isShowShopInfo)
        setFindSameGoodsShop(isShowShopInfo)
    }

    override fun onGoodsSpec() {
        baseViewHolder.setGone(R.id.iv_divider_of_spec_name, false)
        // 规格
        if (TextUtils.isEmpty(rowsBean.spec)) {
            baseViewHolder.setGone(R.id.tv_goods_spec, false)
        } else {
            baseViewHolder.setGone(R.id.tv_goods_spec, true)
            baseViewHolder.setText(R.id.tv_goods_spec, "${rowsBean.spec} | ")
        }
    }

    override fun onFinal() {
        super.onFinal()
        baseViewHolder.getView<TextView?>(R.id.tv_retail_price)?.visibility = View.GONE
        baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe)?.visibility = View.GONE
        baseViewHolder.getView<TextView?>(R.id.shop_price_tv)?.visibility = View.GONE
        baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)?.visibility = View.GONE
//        baseViewHolder.getView<ShopNameWithTagView?>(R.id.rl_icon_type)?.visibility = View.GONE
        baseViewHolder.getView<ShopNameWithTagView>(R.id.data_tag_list_view)?.visibility = View.GONE
        setFindSameGoodsCoupon()
        baseViewHolder.setVisible(R.id.tv_goods_spec, false)
        baseViewHolder.setGone(R.id.tv_validity_period, !TextUtils.isEmpty(rowsBean.nearEffect))

        // 注意：单价显示已在父类 onFinal() 中统一处理，这里不再重复调用
        // handleUnitPrice()
    }

    override fun onShowMiddlePackage(): Boolean = false


    override fun onEffectTags() {
        if (TextUtils.isEmpty(rowsBean.nearEffect)) {
            baseViewHolder.setGone(R.id.tv_validity_period, false)
        } else {
            baseViewHolder.setGone(R.id.tv_validity_period, true)
            baseViewHolder.setText(
                R.id.tv_validity_period,
                "有效期:${rowsBean.nearEffect}"
            )
        }
    }

    override fun onBuyCount() {
        super.onBuyCount()
    }

}