package com.ybmmarket20.bean.payment;

public class PaymentSettleBean {

    // 商品总金额
    private double totalAmount;
    // 活动优惠总额
    private double promoTotalAmt;
    // 优惠券优惠总额
    private double voucherTotalAmt;
    // 运费总额
    private double freightTotalAmt;
    // 余额抵扣总额
    private double availBalanceAmt = 0.00;
    //购物金充值金额1000
    private double rechargeAmount = 0.00;
    // 实付金额
    private double payAmount;
    //一口价
    private double fixedPriceAmount;
    //底部栏运费提示（含运费12.00元）
    private String freightTips;

    /*---------old---------*/
    private double b2bTotalAmount;//总金额
    private double giftTotalAmount;//赠品总金额
    private double payablePrice;//支付金额
    private int varietyNum;//总共
    private double promoDiscountAmount;//全场满减金额
    private double rebate = 0.00;//返点

    public int isHideCoupon; //是否隐藏优惠券 1隐藏; 0展示

    public float virtualGold; //购物金抵扣
    public float redPacketAmount; //红包抵扣

    public String payDiscount; //支付优惠

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public double getPromoTotalAmt() {
        return promoTotalAmt;
    }

    public void setPromoTotalAmt(double promoTotalAmt) {
        this.promoTotalAmt = promoTotalAmt;
    }

    public double getVoucherTotalAmt() {
        return voucherTotalAmt;
    }

    public void setVoucherTotalAmt(double voucherTotalAmt) {
        this.voucherTotalAmt = voucherTotalAmt;
    }

    public double getFreightTotalAmt() {
        return freightTotalAmt;
    }

    public void setFreightTotalAmt(double freightTotalAmt) {
        this.freightTotalAmt = freightTotalAmt;
    }

    public double getAvailBalanceAmt() {
        return availBalanceAmt;
    }

    public void setAvailBalanceAmt(double availBalanceAmt) {
        this.availBalanceAmt = availBalanceAmt;
    }

    public double getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(double payAmount) {
        this.payAmount = payAmount;
    }

    public double getFixedPriceAmount() {
        return fixedPriceAmount;
    }

    public void setFixedPriceAmount(double fixedPriceAmount) {
        this.fixedPriceAmount = fixedPriceAmount;
    }

    public double getB2bTotalAmount() {
        return b2bTotalAmount;
    }

    public void setB2bTotalAmount(double b2bTotalAmount) {
        this.b2bTotalAmount = b2bTotalAmount;
    }

    public double getGiftTotalAmount() {
        return giftTotalAmount;
    }

    public void setGiftTotalAmount(double giftTotalAmount) {
        this.giftTotalAmount = giftTotalAmount;
    }

    public double getPayablePrice() {
        return payablePrice;
    }

    public void setPayablePrice(double payablePrice) {
        this.payablePrice = payablePrice;
    }

    public int getVarietyNum() {
        return varietyNum;
    }

    public void setVarietyNum(int varietyNum) {
        this.varietyNum = varietyNum;
    }

    public double getPromoDiscountAmount() {
        return promoDiscountAmount;
    }

    public void setPromoDiscountAmount(double promoDiscountAmount) {
        this.promoDiscountAmount = promoDiscountAmount;
    }

    public double getRebate() {
        return rebate;
    }

    public void setRebate(double rebate) {
        this.rebate = rebate;
    }

    public String getFreightTips() {
        return freightTips;
    }

    public void setFreightTips(String freightTips) {
        this.freightTips = freightTips;
    }

    public double getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(double rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }
}
