package com.ybmmarket20.bean;

public class VideoPicPreviewEntity {
	public final static int URL_LOCAL_TYPE = 0;
	public final static int URL_NETWORK_TYPE = 1;
	public final static int FILE_PIC_TYPE = 0;
	public final static int FILE_VIDEO_TYPE = 1;
	private int url_type ;
	private int file_type ;
	private String pre_url = "";//本地地址
	private String v_url = "";//要上传的地址或者上传后回来的地址；
	public String getPre_url() {
		return pre_url;
	}
	public void setPre_url(String pre_url) {
		this.pre_url = pre_url;
	}
	public String getV_url() {
		return v_url;
	}
	public void setV_url(String v_url) {
		this.v_url = v_url;
	}
	public int getUrl_type() {
		return url_type;
	}
	public void setUrl_type(int url_type) {
		this.url_type = url_type;
	}
	public int getFile_type() {
		return file_type;
	}
	public void setFile_type(int file_type) {
		this.file_type = file_type;
	}


}
