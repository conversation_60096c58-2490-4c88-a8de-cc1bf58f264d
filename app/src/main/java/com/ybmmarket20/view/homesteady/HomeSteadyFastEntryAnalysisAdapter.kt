package com.ybmmarket20.view.homesteady

import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.bean.homesteady.FastEntryItem
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.home.HomeReportEvent
import com.ybmmarket20.xyyreport.page.home.HomeSubModuleRecordUtil
import com.ybmmarket20.xyyreport.spm.SpmUtil

abstract class HomeSteadyFastEntryAnalysisAdapter(mList: MutableList<FastEntryItem>, mLayout: Int) :
    YBMBaseAdapter<FastEntryItem>(mLayout, mList) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: FastEntryItem?) {
        whenAllNotNull(baseViewHolder, t) { _, bean ->
            SpmUtil.checkAnalysisContext(mContext) {
                val spmE = it.getSpmCtn()?.spmE
                val spmB = it.getSpmCtn()?.spmB
                val spmD = bean.trackData?.spmEntity?.spmD
                val cacheTag = "$spmE$spmD"
                if (!HomeSubModuleRecordUtil.get().isContainsFastEntryRecord(spmB, cacheTag)) {
                    SpmLogUtil.print("首页-组件-快捷入口子模块曝光")
                    val spm = bean.trackData?.spmEntity?.newInstance()
                    val scm = bean.trackData?.scmEntity?.newInstance()
                    HomeReportEvent.trackHomeSubComponentExposure(mContext, spm, scm)
                    HomeSubModuleRecordUtil.get().addFastEntryRecord(spmB, cacheTag)
                }
            }
        }
    }
}