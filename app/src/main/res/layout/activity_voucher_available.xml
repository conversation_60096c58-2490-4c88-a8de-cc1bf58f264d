<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_bg"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.flyco.tablayout.SlidingTabLayout
            android:id="@+id/ps_tab"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@color/white"
            app:tl_indicator_color="@color/base_colors_new"
            app:tl_indicator_corner_radius="2dp"
            app:tl_indicator_height="4dp"
            app:tl_indicator_width_equal_title="true"
            app:tl_tab_padding="11dp"
            app:tl_tab_space_equal="true"
            app:tl_textAllCaps="true"
            app:tl_textBold="BOTH"
            app:tl_textSelectColor="@color/color_292933"
            app:tl_textSelectSize="17sp"
            app:tl_textUnselectColor="@color/text_676773"
            app:tl_textsize="15sp" />


        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_client"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/ps_tab"
            android:layout_marginTop="0.5dp"
            android:background="@color/white" />

    </RelativeLayout>

</LinearLayout>