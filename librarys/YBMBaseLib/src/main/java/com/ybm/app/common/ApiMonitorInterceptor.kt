package com.ybm.app.common

import ly.count.android.sdk.Countly
import ly.count.android.sdk.XyyApmCly
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.internal.http2.ConnectionShutdownException
import org.json.JSONObject
import java.io.EOFException
import java.io.IOException
import java.io.InterruptedIOException
import java.net.*
import javax.net.ssl.*

class ApiMonitorInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val url = request.url

        val sentRequestAtMillis = System.currentTimeMillis()

        var xyyStatus = "success"
        var xyyStatusCode = 10000
        var errorMessage = ""
        var receivedDuration = 0L

        val response: Response
        try {
            response = chain.proceed(request)
            receivedDuration = System.currentTimeMillis() - sentRequestAtMillis
            try {
                val jsonBody = JSONObject(response.peekBody(1024 * 1024).string())

                if (jsonBody.has("msg")) {
                    errorMessage = jsonBody.optString("msg")
                }

                if (jsonBody.has("code")) {
                    xyyStatusCode = jsonBody.optInt("code")
                }

                if (jsonBody.has("status")) {
                    xyyStatus = jsonBody.optString("status")
                }
            } catch (ex: Exception) {
                // do nothing
            }
        } catch (ex: IOException) {
            receivedDuration = System.currentTimeMillis() - sentRequestAtMillis

            xyyStatus = "exception"
            xyyStatusCode = getErrorCode(ex)

            throw ex
        } finally {
            if (Countly.sharedInstance().isInitialized){
                XyyApmCly.getInstance().reportXyyNet(url.encodedPath, url.host, receivedDuration, sentRequestAtMillis, xyyStatusCode, xyyStatus, errorMessage)
            }
        }

        return response
    }


    /**
     * 按照IOException 来返回错误编码
     * 目前按照豆芽crm的逻辑，部分编码可能和ybm冲突
     *
     * ybm 通过抓包的情况如下：
     *
     * { 错误的case
     *      "msg": "xxxx",
     *      "code": 9999,
     *      "status": "failure"
     * }
     *
     * { 成功的case
     *      "msg": "xxxx",
     *      "code": 1000,
     *      "data": {
     *          "num": 0
     *      }
     *      "status": "success"
     * }
     *
     */
    private fun getErrorCode(e: IOException): Int {
        val message = e.message
        return when {
            e is SSLException -> {
                when (e) {
                    is SSLHandshakeException -> {
                        1001
                    }
                    is SSLKeyException -> {
                        1002
                    }
                    is SSLProtocolException -> {
                        1003
                    }
                    is SSLPeerUnverifiedException -> {
                        1004
                    }
                    else -> {
                        1005
                    }
                }
            }
            e is SocketException -> {
                when (e) {
                    is ConnectException -> {
                        2001
                    }
                    is NoRouteToHostException -> {
                        2002
                    }
                    is PortUnreachableException -> {
                        2003
                    }
                    else -> {
                        2000
                    }
                }
            }
            e is SocketTimeoutException -> {
                3001
            }
            e is InterruptedIOException -> {
                3000
            }
            e is ConnectionShutdownException -> {
                3002
            }
            e is EOFException -> {
                3003
            }
            e is UnknownHostException -> {
                4001
            }
            e is UnknownServiceException -> {
                4002
            }
            e is ProtocolException -> {
                5000
            }
            "Canceled" == message -> {
                999
            }
            else -> { // 默认的code，和上面Intercept默认的code一致
                10000
            }
        }
    }
}