package com.ybmmarket20.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;

import com.ybmmarket20.R;

public class SquareImageView extends androidx.appcompat.widget.AppCompatImageView {
	
	public static final int BASE_ON_AUTO = 0;
	public static final int BASE_ON_WIDTH = 1;
	public static final int BASE_ON_HEIGHT = 2;

	private int base_on;

	public SquareImageView(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);
		getType(context, attrs);
	}
	public SquareImageView(Context context, AttributeSet attrs) {
		this(context, attrs,0);
	}
	public SquareImageView(Context context) {
		this(context,null);
	}
	
	protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
		super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int childWidthSize = getMeasuredWidth();
        int childHeightSize = getMeasuredHeight();
        switch (base_on) {
		case BASE_ON_AUTO:
			if (childHeightSize >= childWidthSize) {
	        	 //高度和宽度一样
	            heightMeasureSpec = widthMeasureSpec = MeasureSpec.makeMeasureSpec(childWidthSize, MeasureSpec.EXACTLY);
			}
	        else {
	        	 //宽度和高度一样
	            heightMeasureSpec = widthMeasureSpec = MeasureSpec.makeMeasureSpec(childHeightSize, MeasureSpec.EXACTLY);
			}
			break;
		case BASE_ON_WIDTH:
			 //高度和宽度一样
            heightMeasureSpec = widthMeasureSpec = MeasureSpec.makeMeasureSpec(childWidthSize, MeasureSpec.EXACTLY);
			break;
		case BASE_ON_HEIGHT:
			 //宽度和高度一样
            heightMeasureSpec = widthMeasureSpec = MeasureSpec.makeMeasureSpec(childHeightSize, MeasureSpec.EXACTLY);
			break;
		default:
			break;
		}
        setMeasuredDimension(MeasureSpec.getSize(widthMeasureSpec), MeasureSpec.getSize(heightMeasureSpec));
    }
	
	private void getType(Context context, AttributeSet attributeSet){
		if(attributeSet == null){
			return;
		}
		 TypedArray a = context.obtainStyledAttributes(attributeSet, R.styleable.square_imageview);
	        try {
	        	base_on = a.getInt(R.styleable.square_imageview_base_on, 0);
	        } finally {
	            a.recycle();
	        }
	}

}
